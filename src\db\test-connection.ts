import "dotenv/config";
import { config } from "dotenv";
import postgres from "postgres";
import { URL } from "url";

// Load environment variables
config({ path: ".env.development" });

async function parseAndValidateUrl(databaseUrl: string) {
  try {
    const url = new URL(databaseUrl);
    console.log("🔍 Connection details:");
    console.log("  Protocol:", url.protocol);
    console.log("  Host:", url.hostname);
    console.log("  Port:", url.port);
    console.log("  Database:", url.pathname.slice(1));
    console.log("  Username:", url.username);
    console.log("  Password:", url.password ? "****" : "NOT SET");
    return true;
  } catch (error) {
    console.error("❌ Invalid DATABASE_URL format:", error);
    return false;
  }
}

async function testConnection() {
  const databaseUrl = process.env.DATABASE_URL;

  if (!databaseUrl) {
    console.error("❌ DATABASE_URL is not set");
    process.exit(1);
  }

  console.log("🔗 Testing database connection...");
  console.log("📍 Database URL:", databaseUrl.replace(/:[^:@]*@/, ':****@')); // Hide password

  // Parse and validate URL
  if (!await parseAndValidateUrl(databaseUrl)) {
    process.exit(1);
  }

  try {
    console.log("\n⏳ Attempting to connect...");

    // Create a test connection with more detailed error handling
    const sql = postgres(databaseUrl, {
      prepare: false,
      max: 1,
      connect_timeout: 15,
      idle_timeout: 10,
      debug: false,
      onnotice: (notice) => console.log("📢 Notice:", notice),
    });

    // Test the connection with a simple query
    console.log("🔍 Testing basic connection...");
    const result = await sql`SELECT version()`;
    console.log("✅ Database connection successful!");
    console.log("📊 PostgreSQL version:", result[0].version);

    // Test if we can list tables
    console.log("\n🔍 Checking existing tables...");
    const tables = await sql`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
    `;

    console.log("📋 Current tables in database:", tables.length);
    if (tables.length > 0) {
      tables.forEach(table => console.log("  - " + table.table_name));
    } else {
      console.log("  (No tables found - this is expected for a new database)");
    }

    // Check if we can create a simple table (test permissions)
    console.log("\n🔍 Testing write permissions...");
    try {
      await sql`CREATE TABLE IF NOT EXISTS test_connection (id SERIAL PRIMARY KEY, created_at TIMESTAMP DEFAULT NOW())`;
      await sql`DROP TABLE test_connection`;
      console.log("✅ Write permissions confirmed");
    } catch (writeError) {
      console.warn("⚠️  Write permission test failed:", writeError);
    }

    await sql.end();
    console.log("\n🎉 Connection test completed successfully!");

  } catch (error) {
    console.error("\n❌ Database connection failed:");

    if (error.code === 'XX000') {
      console.error("🔍 This error usually means:");
      console.error("  1. Username or password is incorrect");
      console.error("  2. Database name doesn't exist");
      console.error("  3. User doesn't have permission to access the database");
      console.error("  4. Supabase instance is not properly configured");
    } else if (error.code === 'ECONNREFUSED') {
      console.error("🔍 Connection refused - check:");
      console.error("  1. Host and port are correct");
      console.error("  2. Database server is running");
      console.error("  3. Firewall allows connections");
    } else if (error.code === 'ENOTFOUND') {
      console.error("🔍 Host not found - check the hostname/IP address");
    }

    console.error("\nFull error details:", error);
    process.exit(1);
  }
}

testConnection();
