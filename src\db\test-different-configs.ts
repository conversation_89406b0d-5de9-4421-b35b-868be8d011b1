import "dotenv/config";
import { config } from "dotenv";
import postgres from "postgres";

// Load environment variables
config({ path: ".env.development" });

async function testDifferentConfigurations() {
  const originalUrl = process.env.DATABASE_URL;
  
  if (!originalUrl) {
    console.error("❌ DATABASE_URL is not set");
    process.exit(1);
  }

  // Parse the original URL
  const url = new URL(originalUrl);
  const host = url.hostname;
  const port = url.port;
  const password = url.password;

  console.log("🔍 Testing different database configurations...");
  console.log("📍 Host:", host);
  console.log("📍 Port:", port);
  console.log("📍 Password:", password ? "****" : "NOT SET");

  // Different configurations to try
  const configs = [
    {
      name: "Original configuration",
      url: originalUrl
    },
    {
      name: "Without database name",
      url: `postgresql://postgres:${password}@${host}:${port}/`
    },
    {
      name: "With 'defaultdb' database",
      url: `postgresql://postgres:${password}@${host}:${port}/defaultdb`
    },
    {
      name: "With SSL disabled",
      url: `postgresql://postgres:${password}@${host}:${port}/postgres?sslmode=disable`
    },
    {
      name: "With SSL required",
      url: `postgresql://postgres:${password}@${host}:${port}/postgres?sslmode=require`
    }
  ];

  for (const configTest of configs) {
    console.log(`\n🔍 Testing: ${configTest.name}`);
    console.log(`📍 URL: ${configTest.url.replace(/:[^:@]*@/, ':****@')}`);
    
    try {
      const sql = postgres(configTest.url, {
        prepare: false,
        max: 1,
        connect_timeout: 10,
        ssl: false, // Try without SSL first
      });

      const result = await sql`SELECT 1 as test`;
      console.log(`✅ ${configTest.name} - SUCCESS!`);
      
      // If successful, try to get more info
      try {
        const version = await sql`SELECT version()`;
        console.log(`📊 PostgreSQL version: ${version[0].version}`);
        
        const dbName = await sql`SELECT current_database()`;
        console.log(`📋 Current database: ${dbName[0].current_database}`);
        
        const user = await sql`SELECT current_user`;
        console.log(`👤 Current user: ${user[0].current_user}`);
      } catch (infoError) {
        console.log(`⚠️  Could not get additional info: ${infoError.message}`);
      }
      
      await sql.end();
      
      console.log(`\n🎉 Found working configuration: ${configTest.name}`);
      console.log(`📝 Use this URL in your .env.development:`);
      console.log(`DATABASE_URL="${configTest.url}"`);
      return;
      
    } catch (error) {
      console.log(`❌ ${configTest.name} - FAILED: ${error.message}`);
    }
  }

  console.log("\n💥 All configurations failed. Please check:");
  console.log("1. Your Supabase database password is correct");
  console.log("2. The database user has proper permissions");
  console.log("3. The database instance is properly configured");
}

testDifferentConfigurations();
