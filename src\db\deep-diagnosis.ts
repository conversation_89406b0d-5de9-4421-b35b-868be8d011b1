import "dotenv/config";
import { config } from "dotenv";
import postgres from "postgres";
import { createConnection } from 'net';

// Load environment variables
config({ path: ".env.development" });

async function testRawPostgresProtocol(host: string, port: number, user: string, password: string, database: string) {
  console.log(`\n🔍 Testing raw PostgreSQL protocol handshake...`);
  
  return new Promise((resolve) => {
    const socket = createConnection({ host, port }, () => {
      console.log('✅ TCP connection established');
      
      // Send PostgreSQL startup message
      const startupMessage = Buffer.alloc(1024);
      let offset = 0;
      
      // Protocol version (3.0)
      startupMessage.writeInt32BE(196608, offset); // 3 << 16 | 0
      offset += 4;
      
      // Parameters
      const params = [
        'user', user,
        'database', database,
        'application_name', 'nodejs-test',
        ''
      ];
      
      for (const param of params) {
        startupMessage.write(param, offset);
        offset += param.length + 1; // +1 for null terminator
      }
      
      // Message length (including length field itself)
      const messageLength = offset + 4;
      const message = Buffer.alloc(messageLength);
      message.writeInt32BE(messageLength, 0);
      startupMessage.copy(message, 4, 0, offset);
      
      socket.write(message);
      
      let responseData = Buffer.alloc(0);
      
      socket.on('data', (data) => {
        responseData = Buffer.concat([responseData, data]);
        
        if (responseData.length >= 1) {
          const messageType = responseData[0];
          console.log(`📨 Received message type: ${String.fromCharCode(messageType)} (${messageType})`);
          
          if (messageType === 82) { // 'R' - Authentication request
            console.log('🔐 Authentication request received');
            if (responseData.length >= 9) {
              const authType = responseData.readInt32BE(5);
              console.log(`🔐 Auth type: ${authType}`);
              
              if (authType === 0) {
                console.log('✅ Authentication successful (no password required)');
                resolve(true);
              } else if (authType === 3) {
                console.log('🔑 Clear text password required');
                // Send password
                const passwordMessage = Buffer.alloc(password.length + 6);
                passwordMessage[0] = 112; // 'p'
                passwordMessage.writeInt32BE(password.length + 5, 1);
                passwordMessage.write(password, 5);
                passwordMessage[password.length + 5] = 0;
                socket.write(passwordMessage);
              } else if (authType === 5) {
                console.log('🧂 MD5 password required');
                console.log('⚠️  MD5 auth not implemented in this test');
                resolve(false);
              } else {
                console.log(`❌ Unsupported auth type: ${authType}`);
                resolve(false);
              }
            }
          } else if (messageType === 90) { // 'Z' - Ready for query
            console.log('✅ Ready for query - Authentication successful!');
            resolve(true);
          } else if (messageType === 69) { // 'E' - Error
            console.log('❌ Error response received');
            if (responseData.length >= 5) {
              const errorLength = responseData.readInt32BE(1);
              const errorMessage = responseData.slice(5, 5 + errorLength - 4).toString();
              console.log(`❌ Error: ${errorMessage}`);
            }
            resolve(false);
          }
        }
      });
      
      socket.on('error', (error) => {
        console.log(`❌ Socket error: ${error.message}`);
        resolve(false);
      });
      
      socket.setTimeout(10000, () => {
        console.log('❌ Protocol handshake timeout');
        socket.destroy();
        resolve(false);
      });
    });
    
    socket.on('error', (error) => {
      console.log(`❌ Connection error: ${error.message}`);
      resolve(false);
    });
  });
}

async function testSupabaseSpecificConnection() {
  const host = "***************";
  const password = "bkyvaohkgoz7wzoyh0pcs1ueoyuasgtl";
  
  console.log("🔍 Supabase Deep Connection Diagnosis");
  console.log("=====================================");
  
  // Test different connection approaches
  const testConfigs = [
    {
      port: 5434,
      user: "postgres",
      database: "postgres",
      desc: "Standard Supavisor connection"
    },
    {
      port: 6544,
      user: "postgres", 
      database: "postgres",
      desc: "Alternative Supavisor port"
    },
    {
      port: 5434,
      user: "supabase_admin",
      database: "postgres", 
      desc: "Supabase admin user"
    },
    {
      port: 5434,
      user: "postgres",
      database: "defaultdb",
      desc: "Default database"
    },
    {
      port: 5434,
      user: "authenticator",
      database: "postgres",
      desc: "Supabase authenticator role"
    }
  ];
  
  for (const testConfig of testConfigs) {
    console.log(`\n🧪 Testing: ${testConfig.desc}`);
    console.log(`📍 ${testConfig.user}@${host}:${testConfig.port}/${testConfig.database}`);
    
    // Test raw protocol
    const protocolSuccess = await testRawPostgresProtocol(
      host, 
      testConfig.port, 
      testConfig.user, 
      password, 
      testConfig.database
    );
    
    if (protocolSuccess) {
      console.log(`🎉 Raw protocol test successful for ${testConfig.desc}!`);
      
      // Try with postgres library
      try {
        const url = `postgresql://${testConfig.user}:${password}@${host}:${testConfig.port}/${testConfig.database}`;
        const sql = postgres(url, {
          prepare: false,
          max: 1,
          connect_timeout: 10,
          ssl: false,
        });
        
        const result = await sql`SELECT 1 as test`;
        console.log(`✅ postgres library connection also successful!`);
        
        await sql.end();
        
        console.log(`\n🎉 WORKING CONFIGURATION FOUND!`);
        console.log(`📝 Use this DATABASE_URL:`);
        console.log(`DATABASE_URL="${url}"`);
        return;
        
      } catch (error) {
        console.log(`❌ postgres library failed: ${error.message}`);
      }
    }
  }
  
  console.log("\n💥 All connection attempts failed.");
  console.log("\n🔧 Recommendations:");
  console.log("1. Check if Supabase is using a different authentication method");
  console.log("2. Verify the exact user roles in your Supabase setup");
  console.log("3. Check Supabase logs for authentication errors");
  console.log("4. Consider using the Supabase Dashboard to create a new database user");
}

testSupabaseSpecificConnection();
