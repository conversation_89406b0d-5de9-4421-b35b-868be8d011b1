import "dotenv/config";
import { config } from "dotenv";
import postgres from "postgres";

// Load environment variables
config({ path: ".env.development" });

async function testSupabaseModes() {
  const host = "***************";
  const password = "bkyvaohkgoz7wzoyh0pcs1ueoyuasgtl";

  console.log("🔍 Testing different Supabase connection modes...");

  // Test configurations for self-hosted Supabase
  const configs = [
    {
      name: "Direct PostgreSQL connection (port 5434)",
      url: `postgresql://postgres:${password}@${host}:5434/postgres`,
      options: { prepare: false, max: 1, connect_timeout: 10 }
    },
    {
      name: "Supavisor pooler connection (port 6544)",
      url: `postgresql://postgres:${password}@${host}:6544/postgres`,
      options: { prepare: false, max: 1, connect_timeout: 10 }
    },
    {
      name: "Direct connection with SSL disabled",
      url: `postgresql://postgres:${password}@${host}:5434/postgres?sslmode=disable`,
      options: { prepare: false, max: 1, connect_timeout: 10, ssl: false }
    },
    {
      name: "Pooler connection with SSL disabled",
      url: `postgresql://postgres:${password}@${host}:6544/postgres?sslmode=disable`,
      options: { prepare: false, max: 1, connect_timeout: 10, ssl: false }
    },
    {
      name: "Direct connection to 'defaultdb'",
      url: `postgresql://postgres:${password}@${host}:5434/defaultdb`,
      options: { prepare: false, max: 1, connect_timeout: 10 }
    },
    {
      name: "Connection without database specified",
      url: `postgresql://postgres:${password}@${host}:5434/`,
      options: { prepare: false, max: 1, connect_timeout: 10 }
    }
  ];

  for (const configTest of configs) {
    console.log(`\n🔍 Testing: ${configTest.name}`);
    console.log(`📍 URL: ${configTest.url.replace(/:[^:@]*@/, ':****@')}`);
    
    try {
      const sql = postgres(configTest.url, configTest.options);

      // Test basic connection
      const result = await sql`SELECT 1 as test`;
      console.log(`✅ ${configTest.name} - CONNECTION SUCCESS!`);
      
      // Get database info
      try {
        const version = await sql`SELECT version()`;
        console.log(`📊 PostgreSQL version: ${version[0].version.substring(0, 50)}...`);
        
        const dbName = await sql`SELECT current_database()`;
        console.log(`📋 Current database: ${dbName[0].current_database}`);
        
        const user = await sql`SELECT current_user`;
        console.log(`👤 Current user: ${user[0].current_user}`);

        // Test if we can list schemas
        const schemas = await sql`SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT LIKE 'pg_%' AND schema_name != 'information_schema'`;
        console.log(`📁 Available schemas: ${schemas.map(s => s.schema_name).join(', ')}`);

        // Test if we can list tables
        const tables = await sql`SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'`;
        console.log(`📋 Tables in public schema: ${tables.length}`);
        
      } catch (infoError) {
        console.log(`⚠️  Could not get additional info: ${infoError.message}`);
      }
      
      await sql.end();
      
      console.log(`\n🎉 WORKING CONFIGURATION FOUND!`);
      console.log(`📝 Update your .env.development with:`);
      console.log(`DATABASE_URL="${configTest.url}"`);
      return true;
      
    } catch (error) {
      console.log(`❌ ${configTest.name} - FAILED: ${error.message}`);
      if (error.code) {
        console.log(`   Error code: ${error.code}`);
      }
    }
  }

  console.log("\n💥 All configurations failed.");
  console.log("\n🔧 Troubleshooting suggestions:");
  console.log("1. Check if your Supabase containers are running:");
  console.log("   docker ps | grep supabase");
  console.log("2. Check Supabase logs:");
  console.log("   docker logs <supabase-container-name>");
  console.log("3. Verify the password in your Supabase .env file");
  console.log("4. Make sure the database has been initialized");
  
  return false;
}

testSupabaseModes();
