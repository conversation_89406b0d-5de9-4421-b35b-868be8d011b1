import "dotenv/config";
import { config } from "dotenv";
import postgres from "postgres";
import { createConnection } from 'net';

// Load environment variables
config({ path: ".env.development" });

async function testRawConnection(host: string, port: number) {
  console.log(`🔍 Testing raw TCP connection to ${host}:${port}...`);
  
  return new Promise((resolve, reject) => {
    const socket = createConnection({ host, port }, () => {
      console.log('✅ TCP connection successful');
      socket.end();
      resolve(true);
    });
    
    socket.on('error', (error) => {
      console.error('❌ TCP connection failed:', error.message);
      reject(error);
    });
    
    socket.setTimeout(5000, () => {
      console.error('❌ TCP connection timeout');
      socket.destroy();
      reject(new Error('Connection timeout'));
    });
  });
}

async function testPostgresHandshake(host: string, port: number, user: string, password: string, database: string) {
  console.log(`\n🔍 Testing PostgreSQL handshake...`);
  
  try {
    // Try with minimal configuration
    const sql = postgres(`postgresql://${user}:${password}@${host}:${port}/${database}`, {
      prepare: false,
      max: 1,
      connect_timeout: 10,
      idle_timeout: 5,
      ssl: false,
      debug: false,
      onnotice: (notice) => console.log('📢 PostgreSQL Notice:', notice.message),
      onnotification: (notification) => console.log('📢 PostgreSQL Notification:', notification),
    });

    // Just try to establish connection without running queries
    console.log('⏳ Attempting PostgreSQL handshake...');
    
    // This should trigger the connection
    const result = await sql`SELECT 1`;
    console.log('✅ PostgreSQL handshake successful!');
    
    await sql.end();
    return true;
    
  } catch (error) {
    console.error('❌ PostgreSQL handshake failed:', error.message);
    console.error('Error details:', {
      code: error.code,
      severity: error.severity,
      detail: error.detail,
      hint: error.hint,
      position: error.position,
      where: error.where,
    });
    return false;
  }
}

async function diagnoseSupabase() {
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error("❌ DATABASE_URL is not set");
    process.exit(1);
  }

  const url = new URL(databaseUrl);
  const host = url.hostname;
  const port = parseInt(url.port);
  const user = url.username;
  const password = url.password;
  const database = url.pathname.slice(1);

  console.log("🔍 Supabase Connection Diagnosis");
  console.log("================================");
  console.log(`Host: ${host}`);
  console.log(`Port: ${port}`);
  console.log(`User: ${user}`);
  console.log(`Database: ${database}`);
  console.log(`Password: ${password ? '****' : 'NOT SET'}`);

  // Step 1: Test TCP connection
  try {
    await testRawConnection(host, port);
  } catch (error) {
    console.log("\n💥 TCP connection failed. Check:");
    console.log("1. Supabase containers are running");
    console.log("2. Port mapping is correct");
    console.log("3. Firewall settings");
    return;
  }

  // Step 2: Test PostgreSQL handshake
  const handshakeSuccess = await testPostgresHandshake(host, port, user, password, database);
  
  if (!handshakeSuccess) {
    console.log("\n🔧 Possible solutions:");
    console.log("1. Check if Supabase database is fully initialized:");
    console.log("   docker logs <supabase-db-container>");
    console.log("2. Verify POSTGRES_PASSWORD in Supabase .env file");
    console.log("3. Check if the 'postgres' user exists:");
    console.log("   docker exec -it <supabase-db-container> psql -U postgres -l");
    console.log("4. Try connecting directly to the database container:");
    console.log("   docker exec -it <supabase-db-container> psql -U postgres");
    console.log("5. Check Supabase initialization logs for errors");
    
    // Try alternative configurations
    console.log("\n🔍 Trying alternative configurations...");
    
    const alternatives = [
      { db: 'defaultdb', desc: 'defaultdb database' },
      { db: 'template1', desc: 'template1 database' },
      { db: '', desc: 'no database specified' },
    ];
    
    for (const alt of alternatives) {
      console.log(`\n🔍 Testing with ${alt.desc}...`);
      const success = await testPostgresHandshake(host, port, user, password, alt.db);
      if (success) {
        console.log(`\n🎉 SUCCESS! Use this configuration:`);
        console.log(`DATABASE_URL="postgresql://${user}:${password}@${host}:${port}/${alt.db}"`);
        return;
      }
    }
  }
}

diagnoseSupabase();
