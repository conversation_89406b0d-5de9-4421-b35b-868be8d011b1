import "dotenv/config";
import { config } from "dotenv";
import postgres from "postgres";
import { readFileSync } from "fs";
import { join } from "path";

// Load environment variables
config({ path: ".env.development" });

async function manualMigrate() {
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error("❌ DATABASE_URL is not set");
    process.exit(1);
  }

  console.log("🔗 Attempting to connect and create tables...");

  try {
    // Create connection
    const sql = postgres(databaseUrl, {
      prepare: false,
      max: 1,
      connect_timeout: 15,
      ssl: false,
    });

    console.log("✅ Connected to database");

    // Read the migration file
    const migrationPath = join(process.cwd(), "src/db/migrations/0000_wealthy_squirrel_girl.sql");
    const migrationSQL = readFileSync(migrationPath, "utf-8");

    console.log("📄 Read migration file");

    // Split SQL statements (remove the --> statement-breakpoint comments)
    const statements = migrationSQL
      .split("--> statement-breakpoint")
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    console.log(`🔧 Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        await sql.unsafe(statement);
        console.log(`✅ Statement ${i + 1} executed successfully`);
      } catch (error) {
        console.error(`❌ Error executing statement ${i + 1}:`, error.message);
        // Continue with other statements
      }
    }

    // Verify tables were created
    console.log("\n🔍 Verifying created tables...");
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;

    console.log("📋 Created tables:");
    tables.forEach(table => console.log(`  ✅ ${table.table_name}`));

    await sql.end();
    console.log("\n🎉 Migration completed successfully!");

  } catch (error) {
    console.error("❌ Migration failed:", error.message);
    console.error("Full error:", error);
    process.exit(1);
  }
}

manualMigrate();
