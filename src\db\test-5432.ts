import "dotenv/config";
import { config } from "dotenv";
import postgres from "postgres";

// Load environment variables
config({ path: ".env.development" });

async function testPort5432() {
  const host = "***************";
  const port = 5432;
  
  console.log("🔍 Testing port 5432 with different credentials...");
  console.log("==================================================");

  // Different credential combinations to try
  const credentialTests = [
    { user: "postgres", password: "bkyvaohkgoz7wzoyh0pcs1ueoyuasgtl", desc: "Current Supabase password" },
    { user: "postgres", password: "postgres", desc: "Default postgres password" },
    { user: "postgres", password: "", desc: "No password" },
    { user: "supabase", password: "bkyvaohkgoz7wzoyh0pcs1ueoyuasgtl", desc: "Supabase user" },
    { user: "root", password: "bkyvaohkgoz7wzoyh0pcs1ueoyuasgtl", desc: "Root user" },
  ];

  for (const cred of credentialTests) {
    console.log(`\n🔍 Testing: ${cred.desc}`);
    console.log(`👤 User: ${cred.user}`);
    console.log(`🔑 Password: ${cred.password ? '****' : 'NONE'}`);
    
    const url = `postgresql://${cred.user}:${cred.password}@${host}:${port}/postgres`;
    
    try {
      const sql = postgres(url, {
        prepare: false,
        max: 1,
        connect_timeout: 10,
        ssl: false,
      });

      const result = await sql`SELECT version()`;
      console.log(`✅ SUCCESS! Connection works with ${cred.desc}`);
      console.log(`📊 PostgreSQL version: ${result[0].version.substring(0, 50)}...`);
      
      // Get more info
      try {
        const currentUser = await sql`SELECT current_user`;
        console.log(`👤 Connected as: ${currentUser[0].current_user}`);
        
        const currentDb = await sql`SELECT current_database()`;
        console.log(`📋 Current database: ${currentDb[0].current_database}`);
        
        const tables = await sql`SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'`;
        console.log(`📋 Tables in public schema: ${tables.length}`);
        if (tables.length > 0) {
          console.log(`   Tables: ${tables.map(t => t.table_name).join(', ')}`);
        }
      } catch (infoError) {
        console.log(`⚠️  Could not get additional info: ${infoError.message}`);
      }
      
      await sql.end();
      
      console.log(`\n🎉 WORKING CONFIGURATION FOUND!`);
      console.log(`📝 Update your .env.development:`);
      console.log(`DATABASE_URL="${url}"`);
      return;
      
    } catch (error) {
      console.log(`❌ Failed: ${error.message}`);
    }
  }

  console.log("\n💥 All credential combinations failed for port 5432.");
  console.log("\n🤔 This suggests port 5432 might be your original database, not Supabase.");
  console.log("You mentioned you already have a database on port 5432.");
  console.log("\nNext steps:");
  console.log("1. Check your original database credentials");
  console.log("2. Or focus on fixing the Supabase setup on ports 5434/6544");
}

testPort5432();
