import { createConnection } from 'net';

async function testNetworkConnection() {
  const host = '***************';
  const port = 5434;
  
  console.log(`🔗 Testing network connection to ${host}:${port}...`);
  
  return new Promise((resolve, reject) => {
    const socket = createConnection({ host, port }, () => {
      console.log('✅ Network connection successful!');
      socket.end();
      resolve(true);
    });
    
    socket.on('error', (error) => {
      console.error('❌ Network connection failed:', error.message);
      reject(error);
    });
    
    socket.setTimeout(10000, () => {
      console.error('❌ Connection timeout');
      socket.destroy();
      reject(new Error('Connection timeout'));
    });
  });
}

testNetworkConnection()
  .then(() => {
    console.log('🎉 Network test completed successfully!');
    console.log('📝 The network connection works, so the issue is likely with authentication.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Network test failed:', error.message);
    console.log('📝 Please check:');
    console.log('  1. The host IP address is correct');
    console.log('  2. The port 5434 is correct');
    console.log('  3. Your Supabase instance is running');
    console.log('  4. Firewall allows connections to this port');
    process.exit(1);
  });
