import "dotenv/config";
import { config } from "dotenv";
import postgres from "postgres";
import { createConnection } from 'net';

// Load environment variables
config({ path: ".env.development" });

async function testPort(host: string, port: number, description: string) {
  console.log(`\n🔍 Testing ${description} (${host}:${port})...`);
  
  return new Promise((resolve) => {
    const socket = createConnection({ host, port }, () => {
      console.log(`✅ ${description} - TCP connection successful`);
      socket.end();
      resolve(true);
    });
    
    socket.on('error', (error) => {
      console.log(`❌ ${description} - TCP connection failed: ${error.message}`);
      resolve(false);
    });
    
    socket.setTimeout(5000, () => {
      console.log(`❌ ${description} - Connection timeout`);
      socket.destroy();
      resolve(false);
    });
  });
}

async function testDatabaseConnection(host: string, port: number, description: string) {
  const password = "bkyvaohkgoz7wzoyh0pcs1ueoyuasgtl";
  const url = `postgresql://postgres:${password}@${host}:${port}/postgres`;
  
  console.log(`\n🔍 Testing PostgreSQL connection: ${description}`);
  console.log(`📍 URL: postgresql://postgres:****@${host}:${port}/postgres`);
  
  try {
    const sql = postgres(url, {
      prepare: false,
      max: 1,
      connect_timeout: 10,
      ssl: false,
    });

    const result = await sql`SELECT version()`;
    console.log(`✅ ${description} - PostgreSQL connection successful!`);
    console.log(`📊 Version: ${result[0].version.substring(0, 50)}...`);
    
    await sql.end();
    return true;
    
  } catch (error) {
    console.log(`❌ ${description} - PostgreSQL connection failed: ${error.message}`);
    return false;
  }
}

async function testAllPorts() {
  const host = "***************";
  
  console.log("🔍 Testing all possible Supabase ports...");
  console.log("==========================================");

  // Common Supabase ports
  const portsToTest = [
    { port: 5434, desc: "Supavisor pooler (your current config)" },
    { port: 6544, desc: "Supavisor alternative port" },
    { port: 5432, desc: "Direct PostgreSQL (if exposed)" },
    { port: 54322, desc: "Common Supabase PostgreSQL port" },
    { port: 54321, desc: "Alternative Supabase PostgreSQL port" },
  ];

  // First test TCP connections
  console.log("\n📡 Testing TCP connections...");
  const workingPorts = [];
  
  for (const portTest of portsToTest) {
    const isOpen = await testPort(host, portTest.port, portTest.desc);
    if (isOpen) {
      workingPorts.push(portTest);
    }
  }

  if (workingPorts.length === 0) {
    console.log("\n💥 No ports are accessible. Check:");
    console.log("1. Supabase containers are running");
    console.log("2. Port mappings in docker-compose.yml");
    console.log("3. Firewall settings");
    return;
  }

  // Test PostgreSQL connections on working ports
  console.log("\n🐘 Testing PostgreSQL connections on accessible ports...");
  
  for (const portTest of workingPorts) {
    const success = await testDatabaseConnection(host, portTest.port, portTest.desc);
    if (success) {
      console.log(`\n🎉 WORKING CONFIGURATION FOUND!`);
      console.log(`📝 Update your .env.development:`);
      console.log(`DATABASE_URL="postgresql://postgres:bkyvaohkgoz7wzoyh0pcs1ueoyuasgtl@${host}:${portTest.port}/postgres"`);
      return;
    }
  }

  console.log("\n💥 All PostgreSQL connections failed.");
  console.log("\n🔧 Next steps:");
  console.log("1. Check Supabase database container logs:");
  console.log("   docker logs <supabase-db-container>");
  console.log("2. Verify POSTGRES_PASSWORD in Supabase configuration");
  console.log("3. Check if database is fully initialized");
  console.log("4. Try connecting directly to the database container:");
  console.log("   docker exec -it <supabase-db-container> psql -U postgres");
}

testAllPorts();
